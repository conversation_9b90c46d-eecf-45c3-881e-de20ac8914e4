// PQDI Item Search - Client-side JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('itemSearchForm');
    const resultsSection = document.getElementById('results');
    const resultsContent = document.getElementById('resultsContent');
    const searchBtn = document.querySelector('.search-btn');
    const customFiltersSection = document.getElementById('customFilters');
    const applyFiltersBtn = document.getElementById('applyFilters');
    const clearFiltersBtn = document.getElementById('clearFilters');

    // Store original search results and detailed item data
    let originalResults = [];
    let itemDetailsCache = new Map();

    // Form submission handler
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        // Show loading state
        showLoading();
        
        // Collect form data
        const formData = new FormData(form);
        const searchParams = {};
        
        // Convert FormData to regular object
        for (let [key, value] of formData.entries()) {
            if (value.trim() !== '') {
                searchParams[key] = value;
            }
        }
        
        try {
            // Send search request to our server
            const response = await fetch('/search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(searchParams)
            });
            
            const result = await response.json();
            
            // Display results
            displayResults(result);
            
        } catch (error) {
            console.error('Search error:', error);
            displayError('An error occurred while searching. Please try again.');
        } finally {
            hideLoading();
        }
    });

    function showLoading() {
        searchBtn.innerHTML = '<span class="loading"></span> Searching...';
        searchBtn.disabled = true;
    }

    function hideLoading() {
        searchBtn.innerHTML = 'Search Items';
        searchBtn.disabled = false;
    }

    function displayResults(result) {
        resultsSection.style.display = 'block';

        if (result.success && result.results) {
            const { items, totalResults, currentPage, totalPages } = result.results;

            // Store original results for custom filtering
            originalResults = items;

            // Show custom filters section
            customFiltersSection.style.display = 'block';

            displayItemResults(items, result.message, currentPage, totalPages);
        } else {
            displayError(result.message || 'Search failed');
        }

        // Scroll to results
        resultsSection.scrollIntoView({ behavior: 'smooth' });
    }

    function displayItemResults(items, message, currentPage = 1, totalPages = 1) {
        let resultsHTML = `
            <div class="search-summary">
                <h4>${message}</h4>
                ${totalPages > 1 ? `<p>Page ${currentPage} of ${totalPages}</p>` : ''}
            </div>
        `;

        if (items.length > 0) {
            resultsHTML += '<div class="items-grid">';

            items.forEach(item => {
                const details = item.details;
                const vendorInfo = item.vendorInfo;
                const mobDropInfo = item.mobDropInfo;

                resultsHTML += `
                    <div class="item-card" data-item-id="${item.id}">
                        <div class="item-header">
                            <h5><a href="${item.link}" target="_blank">${item.name}</a></h5>
                            ${details ? `
                                <div class="item-flags">
                                    ${details.isLore ? '<span class="flag lore">LORE</span>' : ''}
                                    ${details.isNoDrop ? '<span class="flag nodrop">NODROP</span>' : ''}
                                    ${details.isMagic ? '<span class="flag magic">MAGIC</span>' : ''}
                                    ${vendorInfo && vendorInfo.isVendorPurchasable ? '<span class="flag vendor">VENDOR</span>' : ''}
                                    ${mobDropInfo && mobDropInfo.isMobDropped ? '<span class="flag mobdrop">MOB DROP</span>' : ''}
                                </div>
                            ` : ''}
                        </div>
                        <div class="item-stats">
                            ${details && details.baseDamage ? `<span class="stat">DMG: ${details.baseDamage}/${details.delay}</span>` :
                              item.damage && item.delay ? `<span class="stat">DMG: ${item.damage}/${item.delay}</span>` : ''}
                            ${details && details.ratio ? `<span class="stat">Ratio: ${details.ratio.toFixed(3)}</span>` : ''}
                            ${item.ac ? `<span class="stat">AC: ${item.ac}</span>` : ''}
                            ${item.hp ? `<span class="stat">HP: ${item.hp > 0 ? '+' : ''}${item.hp}</span>` : ''}
                            ${item.mana ? `<span class="stat">Mana: ${item.mana > 0 ? '+' : ''}${item.mana}</span>` : ''}
                            ${item.str ? `<span class="stat">STR: ${item.str > 0 ? '+' : ''}${item.str}</span>` : ''}
                            ${item.dex ? `<span class="stat">DEX: ${item.dex > 0 ? '+' : ''}${item.dex}</span>` : ''}
                            ${item.agi ? `<span class="stat">AGI: ${item.agi > 0 ? '+' : ''}${item.agi}</span>` : ''}
                            ${item.sta ? `<span class="stat">STA: ${item.sta > 0 ? '+' : ''}${item.sta}</span>` : ''}
                            ${item.int ? `<span class="stat">INT: ${item.int > 0 ? '+' : ''}${item.int}</span>` : ''}
                            ${item.wis ? `<span class="stat">WIS: ${item.wis > 0 ? '+' : ''}${item.wis}</span>` : ''}
                            ${item.cha ? `<span class="stat">CHA: ${item.cha > 0 ? '+' : ''}${item.cha}</span>` : ''}
                        </div>
                        ${details && details.platinumValue > 0 ? `
                            <div class="item-value">Value: ${details.platinumValue}p ${details.goldValue}g ${details.silverValue}s ${details.copperValue}c</div>
                        ` : ''}
                        ${vendorInfo && vendorInfo.isVendorPurchasable && vendorInfo.vendors.length > 0 ? `
                            <div class="item-vendors">
                                <strong>Sold by:</strong> ${vendorInfo.vendors.map(vendor => vendor.name).join(', ')}
                            </div>
                        ` : ''}
                        ${item.slot || (details && details.slot) ? `<div class="item-slot">Slot: ${details ? details.slot : item.slot}</div>` : ''}
                        ${item.classes ? `<div class="item-classes">Classes: ${item.classes.join(', ')}</div>` : ''}
                        ${item.races ? `<div class="item-races">Races: ${item.races.join(', ')}</div>` : ''}
                        <div class="item-loading" style="display: none;">
                            <span class="loading"></span> Loading details...
                        </div>
                    </div>
                `;
            });

            resultsHTML += '</div>';
        } else {
            resultsHTML += '<div class="no-results">No items found matching your criteria.</div>';
        }

        resultsContent.innerHTML = resultsHTML;
    }

    function displayError(message) {
        resultsSection.style.display = 'block';
        resultsContent.innerHTML = `
            <div class="error-message">
                <h4>Error</h4>
                <p>${message}</p>
            </div>
        `;
    }

    // Form validation and enhancement
    const itemNameInput = document.getElementById('itemName');
    const effectNameInput = document.getElementById('effectName');
    
    // Add input validation
    function validateForm() {
        const itemName = itemNameInput.value.trim();
        const effectName = effectNameInput.value.trim();
        const hasStats = document.querySelector('select[name="stat1"]').value !== '';
        const hasResist = document.querySelector('select[name="resist"]').value !== '';
        const hasContainer = document.getElementById('container').value !== '';
        
        // At least one search criteria should be provided
        if (!itemName && !effectName && !hasStats && !hasResist && !hasContainer) {
            return false;
        }
        
        return true;
    }

    // Real-time form validation
    form.addEventListener('input', function() {
        const isValid = validateForm();
        searchBtn.disabled = !isValid;
        
        if (!isValid) {
            searchBtn.style.opacity = '0.6';
        } else {
            searchBtn.style.opacity = '1';
        }
    });

    // Enhanced select interactions
    const selects = document.querySelectorAll('select');
    selects.forEach(select => {
        select.addEventListener('change', function() {
            if (this.value) {
                this.style.borderColor = 'var(--accent-primary)';
            } else {
                this.style.borderColor = 'var(--border-color)';
            }
        });
    });

    // Enhanced input interactions
    const inputs = document.querySelectorAll('input[type="text"], input[type="number"]');
    inputs.forEach(input => {
        input.addEventListener('input', function() {
            if (this.value.trim()) {
                this.style.borderColor = 'var(--accent-primary)';
            } else {
                this.style.borderColor = 'var(--border-color)';
            }
        });
    });

    // Checkbox enhancements
    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const label = this.closest('label');
            if (this.checked) {
                label.style.color = 'var(--accent-primary)';
            } else {
                label.style.color = 'var(--text-primary)';
            }
        });
    });

    // Clear form functionality
    function clearForm() {
        form.reset();
        resultsSection.style.display = 'none';
        
        // Reset visual states
        selects.forEach(select => {
            select.style.borderColor = 'var(--border-color)';
        });
        
        inputs.forEach(input => {
            input.style.borderColor = 'var(--border-color)';
        });
        
        checkboxes.forEach(checkbox => {
            const label = checkbox.closest('label');
            label.style.color = 'var(--text-primary)';
        });
    }

    // Add clear button
    const clearBtn = document.createElement('button');
    clearBtn.type = 'button';
    clearBtn.className = 'clear-btn';
    clearBtn.textContent = 'Clear Form';
    clearBtn.style.cssText = `
        background: transparent;
        color: var(--text-secondary);
        border: 2px solid var(--border-color);
        padding: 10px 20px;
        border-radius: 8px;
        cursor: pointer;
        margin-left: 15px;
        transition: all 0.3s ease;
    `;
    
    clearBtn.addEventListener('mouseenter', function() {
        this.style.borderColor = 'var(--border-hover)';
        this.style.color = 'var(--text-primary)';
    });
    
    clearBtn.addEventListener('mouseleave', function() {
        this.style.borderColor = 'var(--border-color)';
        this.style.color = 'var(--text-secondary)';
    });
    
    clearBtn.addEventListener('click', clearForm);

    document.querySelector('.submit-section').appendChild(clearBtn);

    // Custom filter event handlers
    applyFiltersBtn.addEventListener('click', applyCustomFilters);
    clearFiltersBtn.addEventListener('click', clearCustomFilters);

    // Function to fetch detailed item information
    async function fetchItemDetails(itemId) {
        if (itemDetailsCache.has(itemId)) {
            return itemDetailsCache.get(itemId);
        }

        try {
            const response = await fetch(`/item-tooltip/${itemId}`);
            const result = await response.json();

            if (result.success) {
                itemDetailsCache.set(itemId, result.details);
                return result.details;
            } else {
                console.error('Failed to fetch item details:', result.message);
                return null;
            }
        } catch (error) {
            console.error('Error fetching item details:', error);
            return null;
        }
    }

    // Function to fetch vendor information for an item
    async function fetchVendorInfo(itemId) {
        try {
            const response = await fetch(`/vendor-check/${itemId}`);
            const result = await response.json();

            if (result.success) {
                return {
                    isVendorPurchasable: result.isVendorPurchasable,
                    vendors: result.vendors
                };
            } else {
                console.error('Failed to fetch vendor info:', result.message);
                return { isVendorPurchasable: false, vendors: [] };
            }
        } catch (error) {
            console.error('Error fetching vendor info:', error);
            return { isVendorPurchasable: false, vendors: [] };
        }
    }

    // Function to fetch mob drop information for an item
    async function fetchMobDropInfo(itemId) {
        try {
            const response = await fetch(`/mobdrop-check/${itemId}`);
            const result = await response.json();

            if (result.success) {
                return {
                    isMobDropped: result.isMobDropped
                };
            } else {
                console.error('Failed to fetch mob drop info:', result.message);
                return { isMobDropped: false };
            }
        } catch (error) {
            console.error('Error fetching mob drop info:', error);
            return { isMobDropped: false };
        }
    }

    // Function to apply custom filters
    async function applyCustomFilters() {
        if (originalResults.length === 0) {
            return;
        }

        // Show loading state
        applyFiltersBtn.innerHTML = '<span class="loading"></span> Applying Filters...';
        applyFiltersBtn.disabled = true;

        // Get filter values
        const loreFilter = document.getElementById('loreFilter').value;
        const nodropFilter = document.getElementById('nodropFilter').value;
        const minValue = parseFloat(document.getElementById('minValue').value) || 0;
        const maxValue = parseFloat(document.getElementById('maxValue').value) || Infinity;
        const minRatio = parseFloat(document.getElementById('minRatio').value) || 0;
        const vendorFilter = document.getElementById('vendorFilter').value;
        const mobDropFilter = document.getElementById('mobDropFilter').value;

        // Fetch detailed information for all items
        const itemsWithDetails = [];

        for (const item of originalResults) {
            if (!item.id) continue;

            const details = await fetchItemDetails(item.id);
            let vendorInfo = { isVendorPurchasable: false, vendors: [] };
            let mobDropInfo = { isMobDropped: false };

            // Only fetch vendor info if vendor filter is applied
            if (vendorFilter) {
                vendorInfo = await fetchVendorInfo(item.id);
            }

            // Only fetch mob drop info if mob drop filter is applied
            if (mobDropFilter) {
                mobDropInfo = await fetchMobDropInfo(item.id);
            }

            if (details) {
                itemsWithDetails.push({
                    ...item,
                    details: details,
                    vendorInfo: vendorInfo,
                    mobDropInfo: mobDropInfo
                });
            }
        }

        // Apply filters
        const filteredItems = itemsWithDetails.filter(item => {
            const details = item.details;
            const vendorInfo = item.vendorInfo;
            const mobDropInfo = item.mobDropInfo;

            // Lore filter
            if (loreFilter === 'only' && !details.isLore) return false;
            if (loreFilter === 'exclude' && details.isLore) return false;

            // NoDrop filter
            if (nodropFilter === 'only' && !details.isNoDrop) return false;
            if (nodropFilter === 'exclude' && details.isNoDrop) return false;

            // Value filter
            if (details.platinumValue < minValue || details.platinumValue > maxValue) return false;

            // Ratio filter
            if (details.ratio < minRatio) return false;

            // Vendor filter
            if (vendorFilter === 'only' && !vendorInfo.isVendorPurchasable) return false;
            if (vendorFilter === 'exclude' && vendorInfo.isVendorPurchasable) return false;

            // Mob drop filter
            if (mobDropFilter === 'only' && !mobDropInfo.isMobDropped) return false;
            if (mobDropFilter === 'exclude' && mobDropInfo.isMobDropped) return false;

            return true;
        });

        // Update display with filtered results
        const filteredCount = filteredItems.length;
        const originalCount = originalResults.length;
        const message = `Showing ${filteredCount} of ${originalCount} items (filtered)`;

        displayItemResults(filteredItems, message);

        // Restore button state
        applyFiltersBtn.innerHTML = 'Apply Filters';
        applyFiltersBtn.disabled = false;
    }

    // Function to clear custom filters
    function clearCustomFilters() {
        document.getElementById('loreFilter').value = '';
        document.getElementById('nodropFilter').value = '';
        document.getElementById('minValue').value = '';
        document.getElementById('maxValue').value = '';
        document.getElementById('minRatio').value = '';
        document.getElementById('vendorFilter').value = '';
        document.getElementById('mobDropFilter').value = '';

        // Reset to original results
        if (originalResults.length > 0) {
            const message = `Found ${originalResults.length} items`;
            displayItemResults(originalResults, message);
        }
    }

    // Initialize form state
    searchBtn.disabled = !validateForm();
    if (!validateForm()) {
        searchBtn.style.opacity = '0.6';
    }
});

// Add some CSS for the results display
const style = document.createElement('style');
style.textContent = `
    .search-info {
        background: var(--bg-primary);
        padding: 20px;
        border-radius: 8px;
        border: 1px solid var(--border-color);
    }
    
    .search-info h4 {
        color: var(--accent-primary);
        margin-bottom: 15px;
    }
    
    .search-info pre {
        background: var(--bg-secondary);
        padding: 15px;
        border-radius: 6px;
        overflow-x: auto;
        font-size: 0.9rem;
        border: 1px solid var(--border-color);
    }
    
    .search-info .note {
        margin-top: 15px;
        color: var(--text-secondary);
        font-style: italic;
    }
    
    .error-message {
        background: rgba(244, 67, 54, 0.1);
        border: 1px solid var(--error-color);
        padding: 20px;
        border-radius: 8px;
        color: var(--error-color);
    }
    
    .error-message h4 {
        margin-bottom: 10px;
    }

    .item-flags {
        display: flex;
        gap: 5px;
        margin-top: 5px;
        flex-wrap: wrap;
    }

    .flag {
        font-size: 0.7rem;
        padding: 2px 6px;
        border-radius: 3px;
        font-weight: bold;
        text-transform: uppercase;
    }

    .flag.lore {
        background: rgba(255, 215, 0, 0.2);
        color: #ffd700;
        border: 1px solid #ffd700;
    }

    .flag.nodrop {
        background: rgba(255, 69, 0, 0.2);
        color: #ff4500;
        border: 1px solid #ff4500;
    }

    .flag.magic {
        background: rgba(138, 43, 226, 0.2);
        color: #8a2be2;
        border: 1px solid #8a2be2;
    }

    .flag.vendor {
        background: rgba(76, 175, 80, 0.2);
        color: #4caf50;
        border: 1px solid #4caf50;
    }

    .flag.mobdrop {
        background: rgba(255, 152, 0, 0.2);
        color: #ff9800;
        border: 1px solid #ff9800;
    }

    .item-value {
        font-size: 0.9rem;
        color: var(--text-secondary);
        margin: 8px 0;
        padding: 5px 8px;
        background: var(--bg-secondary);
        border-radius: 4px;
        border: 1px solid var(--border-color);
        font-family: monospace;
    }

    .item-vendors {
        font-size: 0.9rem;
        color: var(--text-secondary);
        margin: 8px 0;
        padding: 5px 8px;
        background: rgba(76, 175, 80, 0.1);
        border-radius: 4px;
        border: 1px solid rgba(76, 175, 80, 0.3);
    }

    .item-vendors strong {
        color: #4caf50;
    }
`;
document.head.appendChild(style);
